import json
import os
import sys
import pandas as pd
import re
import logging
import time
import traceback
from datetime import datetime

# Configure logging
def setup_logging():
    """Set up logging to both console and file."""
    # Create logs directory if it doesn't exist
    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs")
    os.makedirs(logs_dir, exist_ok=True)

    # Use a single log file named "log"
    log_file = os.path.join(logs_dir, "log")

    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Clear any existing handlers to prevent duplicate logs
    if logger.handlers:
        logger.handlers.clear()

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Create file handler with append mode
    file_handler = logging.FileHandler(log_file, mode='a')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Set up logging
logger = setup_logging()

def process_json_and_generate_report(timestamped_dir: str) -> str:
    """
    Process JSON files and generate Excel report.

    Args:
        timestamped_dir: Path to the directory containing the response folder

    Returns:
        Path to the generated Excel file
    """
    logger.info(f"Starting Excel report generation for: {timestamped_dir}")
    start_time = time.time()

    response_folder = os.path.join(timestamped_dir, "response")
    output_folder = os.path.join(timestamped_dir, "output")

    logger.info(f"Response folder: {response_folder}")
    logger.info(f"Output folder: {output_folder}")

    # Check if response folder exists, raise exception if not
    if not os.path.exists(response_folder):
        raise FileNotFoundError(f"The 'response' folder does not exist at {response_folder}")

    # Create the output folder if it does not exist
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # Define the file path for the output Excel file
    output_file = os.path.join(output_folder, "transactions.xlsx")

    # Function to clean and convert amount to float
    def clean_amount(amount):
        if not amount or pd.isna(amount):  # Handle None, empty, or NA values
            return pd.NA

        # Convert to string to ensure we can process it
        amount_str = str(amount)

        # Remove commas
        amount_str = amount_str.replace(',', '')

        # Remove all special characters except for the decimal point and minus sign
        # This regex keeps only digits, decimal points, and minus signs
        amount_str = re.sub(r'[^\d.\-]', '', amount_str)

        try:
            # Convert to float
            amount_float = float(amount_str)
            # Make negative amounts positive
            return abs(amount_float)
        except (ValueError, TypeError):
            return pd.NA

    # Function to clean description text by replacing <br> tags with a space
    def clean_description(description):
        if not description or pd.isna(description):  # Handle None, empty, or NA values
            return pd.NA

        # Convert to string to ensure we can process it
        description_str = str(description)

        # Replace <br> tags (case insensitive) with a single space
        description_str = re.sub(r'<br\s*/?>', ' ', description_str, flags=re.IGNORECASE)

        return description_str

    # Function to transform date by appending year from start_date
    def transform_date(date_str, start_year):
        if not date_str or pd.isna(date_str):  # Handle None, empty, or NA values
            return pd.NA
        try:
            # Clean the date string
            date_str = str(date_str).strip()
            # logger.info(f"Transforming date: '{date_str}' with start_year: '{start_year}'")

            # Check if the date already has a year component
            if len(date_str.split('/')) >= 3:
                # Date already has year, just parse it
                logger.info(f"Date '{date_str}' already has year component")
                result = pd.to_datetime(date_str, format="%m/%d/%Y", errors='coerce')
                logger.info(f"Parsed date with existing year: {result}")
                return result

            # Make sure start_year is a 4-digit year
            if start_year and len(start_year) == 2:
                # Convert 2-digit year to 4-digit year
                century = "20" if int(start_year) < 50 else "19"  # Assume 00-49 is 2000s, 50-99 is 1900s
                full_year = f"{century}{start_year}"
                # logger.info(f"Converted 2-digit year '{start_year}' to 4-digit year '{full_year}'")
            elif start_year and len(start_year) == 4:
                full_year = start_year
                logger.info(f"Using provided 4-digit year: {full_year}")
            else:
                # If we can't determine the year, use current year
                full_year = str(datetime.now().year)
                logger.info(f"Using current year as fallback: {full_year}")

            # Append the year to the date string
            full_date = f"{date_str}/{full_year}"
            # logger.info(f"Full date string: '{full_date}'")

            # Try to parse with different formats
            for fmt in ["%m/%d/%Y", "%d/%m/%Y"]:
                try:
                    result = pd.to_datetime(full_date, format=fmt)
                    # logger.info(f"Successfully parsed date with format '{fmt}': {result}")
                    return result
                except ValueError:
                    logger.info(f"Failed to parse date with format '{fmt}'")
                    continue

            # If all formats fail, try a more flexible approach
            logger.info(f"Trying flexible date parsing for: '{full_date}'")
            result = pd.to_datetime(full_date, errors='coerce')
            logger.info(f"Flexible parsing result: {result}")
            return result

        except (ValueError, TypeError) as e:
            logger.error(f"Error parsing date '{date_str}': {e}")
            return pd.NA

    # Step 1: Look for a JSON file containing "OpeningPage" in its name
    start_year = None
    previous_balance = 0.0  # Default to 0.0 if not found
    start_date = None
    for filename in os.listdir(response_folder):
        if "openingpage" in filename.lower() and filename.endswith(".json"):
            file_path = os.path.join(response_folder, filename)
            with open(file_path, 'r') as file:
                data = json.load(file)
                previous_balance_raw = data.get("Previous Balance")
                start_date = data.get("Statement Period", {}).get("Start Date")
                if previous_balance_raw:
                    previous_balance_cleaned = clean_amount(previous_balance_raw)
                    if not pd.isna(previous_balance_cleaned):
                        previous_balance = previous_balance_cleaned
                if start_date:
                    try:
                        start_year = start_date.split('/')[-1]
                    except (ValueError, TypeError):
                        start_year = None
                break
    else:
        print("No JSON file containing 'OpeningPage' in its name found in the folder.")
        return

    # Step 2: Initialize an empty DataFrame for credits, debits, and checks
    df = pd.DataFrame(columns=["Date", "Description", "Credit", "Debit"])

    # Step 3: Look for a JSON file containing "CreditPages" in its name
    for filename in os.listdir(response_folder):
        if "creditpages" in filename.lower() and filename.endswith(".json"):
            file_path = os.path.join(response_folder, filename)
            with open(file_path, 'r') as file:
                data = json.load(file)
                credits = data.get("CreditsInfo", [])
                credit_df = pd.DataFrame(credits, columns=["Date", "Description", "Amount"])
                credit_df["Amount"] = credit_df["Amount"].apply(clean_amount)
                credit_df["Description"] = credit_df["Description"].apply(clean_description)
                credit_df = credit_df.rename(columns={"Amount": "Credit"})
                credit_df["Debit"] = pd.NA
                if start_year:
                    credit_df["Date"] = credit_df["Date"].apply(lambda x: transform_date(x, start_year))
                if not credit_df.empty:
                    df = pd.concat([df, credit_df], ignore_index=True)
                break
    else:
        print("No JSON file containing 'CreditPages' in its name found in the folder.")

    # Step 4: Look for a JSON file containing "DebitPages" in its name
    for filename in os.listdir(response_folder):
        if "debitpages" in filename.lower() and filename.endswith(".json"):
            file_path = os.path.join(response_folder, filename)
            with open(file_path, 'r') as file:
                data = json.load(file)
                debits = data.get("DebitInfo", [])
                debit_df = pd.DataFrame(debits, columns=["Date", "Description", "Amount"])
                debit_df["Amount"] = debit_df["Amount"].apply(clean_amount)
                debit_df["Description"] = debit_df["Description"].apply(clean_description)
                debit_df = debit_df.rename(columns={"Amount": "Debit"})
                debit_df["Credit"] = pd.NA
                if start_year:
                    debit_df["Date"] = debit_df["Date"].apply(lambda x: transform_date(x, start_year))
                if not debit_df.empty:
                    df = pd.concat([df, debit_df], ignore_index=True)
                break
    else:
        print("No JSON file containing 'DebitPages' in its name found in the folder.")

    # Step 5: Look for a JSON file containing "CheckPages" in its name
    for filename in os.listdir(response_folder):
        if "checkpages" in filename.lower() and filename.endswith(".json"):
            file_path = os.path.join(response_folder, filename)
            with open(file_path, 'r') as file:
                data = json.load(file)
                checks = data.get("CheckNumberinOrder", [])
                check_df = pd.DataFrame(checks, columns=["Date", "CheckNo", "Amount"])
                check_df["Amount"] = check_df["Amount"].apply(clean_amount)
                check_df = check_df.rename(columns={"Date": "Date", "CheckNo": "Description", "Amount": "Debit"})
                check_df["Description"] = check_df["Description"].apply(clean_description)
                check_df["Credit"] = pd.NA
                if start_year:
                    check_df["Date"] = check_df["Date"].apply(lambda x: transform_date(x, start_year))
                if not check_df.empty:
                    df = pd.concat([df, check_df], ignore_index=True)
                break
    else:
        print("No JSON file containing 'CheckPages' in its name found in the folder.")

    # Step 6: Convert Credit and Debit columns to float type
    df["Credit"] = df["Credit"].astype("float64", errors="ignore")
    df["Debit"] = df["Debit"].astype("float64", errors="ignore")

    # Step 7: Add the Amount column
    df["Amount"] = pd.NA
    df.loc[df["Debit"].notna(), "Amount"] = df["Debit"]
    df.loc[df["Credit"].notna(), "Amount"] = -df["Credit"]
    df["Amount"] = df["Amount"].astype("float64", errors="ignore")

    # Step 8: Remove rows that have a value in only one column
    # Count non-NA values in each row (excluding Date which is always present)
    non_na_count = df.iloc[:, 1:].notna().sum(axis=1)  # Count non-NA values starting from Description column
    # Keep rows that have at least 2 non-NA values (more than just one column with data)
    df = df[non_na_count >= 2].reset_index(drop=True)

    # Step 9: Sort the DataFrame by Date
    df = df.sort_values(by="Date").reset_index(drop=True)

    # Step 9: Add the Balance column
    df["Balance"] = pd.NA
    df["Balance"] = previous_balance - df["Amount"].cumsum()
    df["Balance"] = df["Balance"].astype("float64", errors="ignore")

    # Step 10: Reorder columns to match desired layout: Date, Description, Debit, Credit, Amount, Balance
    df = df[["Date", "Description", "Debit", "Credit", "Amount", "Balance"]]

    # Step 10.5: Format currency columns for display (this helps with Excel display)
    def format_currency(value):
        """Format numeric values as currency strings for better Excel display"""
        if pd.isna(value) or value is None:
            return None
        try:
            return float(value)  # Keep as float for Excel formatting
        except (ValueError, TypeError):
            return None

    # Apply formatting to currency columns
    for col in ["Debit", "Credit", "Amount", "Balance"]:
        df[col] = df[col].apply(format_currency)

    # Step 11: Save the DataFrame to Excel with the updated layout
    heading_part1 = f"Balance as on {start_date if start_date else 'Unknown'}:"
    heading_part2 = previous_balance

    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Sheet1', startrow=4, index=False)
        workbook = writer.book
        worksheet = writer.sheets['Sheet1']

        worksheet['A1'] = heading_part1
        worksheet['A2'] = heading_part2

        # Updated column positions: C=Debit, D=Credit
        worksheet['C1'] = "TOTAL DEBIT TRANSACTIONS"
        worksheet['D1'] = "TOTAL CREDIT TRANSACTIONS"
        worksheet['C2'] = "=SUBTOTAL(3,C6:C1000)"
        worksheet['D2'] = "=SUBTOTAL(3,D6:D1000)"

        worksheet['C3'] = "Total Debit Amount"
        worksheet['D3'] = "Total Credit Amount"
        worksheet['C4'] = "=SUBTOTAL(9,C6:C1000)"
        worksheet['D4'] = "=SUBTOTAL(9,D6:D1000)"

        worksheet.auto_filter.ref = "A5:F5"

        # Apply USD currency formatting to Debit, Credit, Amount, and Balance columns
        from openpyxl.styles import NamedStyle

        # Create USD currency style
        currency_style = NamedStyle(name="currency_usd")
        currency_style.number_format = '"$"#,##0.00_);("$"#,##0.00)'

        # Apply currency formatting to data rows (starting from row 6, which is startrow=4 + header row + 1)
        # Get the last row with data
        last_row = len(df) + 5  # startrow=4 + header row + data rows

        # Format Debit column (C)
        for row in range(6, last_row + 1):
            worksheet[f'C{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'

        # Format Credit column (D)
        for row in range(6, last_row + 1):
            worksheet[f'D{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'

        # Format Amount column (E)
        for row in range(6, last_row + 1):
            worksheet[f'E{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'

        # Format Balance column (F)
        for row in range(6, last_row + 1):
            worksheet[f'F{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'

        # Format the summary cells as well
        worksheet['A2'].number_format = '"$"#,##0.00_);("$"#,##0.00)'
        worksheet['C2'].number_format = '#,##0'  # Count format for transaction count
        worksheet['D2'].number_format = '#,##0'  # Count format for transaction count
        worksheet['C4'].number_format = '"$"#,##0.00_);("$"#,##0.00)'  # Total amounts
        worksheet['D4'].number_format = '"$"#,##0.00_);("$"#,##0.00)'  # Total amounts

    logger.info(f"DataFrame has been saved to {output_file}")

    # Calculate and log processing time
    end_time = time.time()
    processing_time = end_time - start_time
    logger.info(f"Excel report generation completed in {processing_time:.2f} seconds")

    # Print a message to the console as well
    print(f"\n✅ Excel report has been saved to {output_file}")
    print(f"Processing time: {processing_time:.2f} seconds")

    return output_file

if __name__ == "__main__":
    try:
        # Call the function with the folder path (timestamped_dir)
        print("Excel Report Generator")
        print("=====================")
        timestamped_dir = r"Data1\20250609_180202_621a177c"# input("Enter the path to the timestamped directory: ")

        if not os.path.exists(timestamped_dir):
            print(f"❌ Error: Directory does not exist: {timestamped_dir}")
            sys.exit(1)

        output_file = process_json_and_generate_report(timestamped_dir)
        print(f"\n✅ Success! Excel report generated at: {output_file}")

    except Exception as e:
        print(f"\n❌ Error: {e}")
        traceback.print_exc()
        sys.exit(1)